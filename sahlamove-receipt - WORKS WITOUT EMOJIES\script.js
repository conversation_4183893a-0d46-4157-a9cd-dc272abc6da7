// Enhanced SahlaMove Receipt Generator Script

// Configuration constants
const PDF_CONFIG = {
  pageWidth: 210,
  pageHeight: 297,
  margin: 15,
  headerHeight: 35,
  lineHeight: 7,
  tableRowHeight: 8
};

// Mission counter for unique IDs (in real app, this would come from database)
let missionCounter = 1;

// File management utilities
const FileUtils = {
  // Generate mission filename with proper structure
  generateMissionFilename: (date) => {
    const missionDate = new Date(date);
    const year = missionDate.getFullYear();
    const month = String(missionDate.getMonth() + 1).padStart(2, '0');
    const day = String(missionDate.getDate()).padStart(2, '0');
    const missionId = String(missionCounter).padStart(3, '0');

    const folderPath = `missions/${year}/${month}/${day}`;
    const filename = `SM-${year}${month}${day}-${missionId}.pdf`;

    missionCounter++; // Increment for next mission

    return {
      folderPath,
      filename,
      fullPath: `${folderPath}/${filename}`
    };
  }
};

// Utility functions
const PDFUtils = {
  // Helper function to add text with word wrapping
  addWrappedText: (doc, text, x, y, maxWidth, lineHeight = 7) => {
    const lines = doc.splitTextToSize(text, maxWidth);
    doc.text(lines, x, y);
    return y + (lines.length * lineHeight);
  },

  // Helper function to draw a table row with background
  drawTableRow: (doc, data, x, y, widths, height = 8, isHeader = false) => {
    const totalWidth = widths.reduce((a, b) => a + b, 0);
    
    if (isHeader) {
      doc.setFillColor(52, 73, 94); // Dark blue-gray
      doc.setTextColor(255, 255, 255); // White text
      doc.rect(x, y - 6, totalWidth, height, 'F');
    } else {
      doc.setFillColor(248, 249, 250); // Light gray
      doc.rect(x, y - 6, totalWidth, height, 'F');
      doc.setTextColor(0, 0, 0); // Black text
    }
    
    let currentX = x;
    data.forEach((text, index) => {
      if (text && text.toString().trim() !== '') {
        // Split long text if needed
        const maxWidth = widths[index] - 4;
        const lines = doc.splitTextToSize(text.toString(), maxWidth);
        doc.text(lines, currentX + 2, y);
      }
      currentX += widths[index];
    });
    
    return y + height;
  },

  // Add footer to current page
  addPageFooter: (doc) => {
    const pageHeight = PDF_CONFIG.pageHeight;
    const today = new Date().toLocaleDateString('fr-FR');

    // Add separator line
    doc.setDrawColor(200, 200, 200);
    doc.setLineWidth(0.5);
    doc.line(15, pageHeight - 25, 195, pageHeight - 25);

    // Footer content
    doc.setFontSize(8);
    doc.setTextColor(128, 128, 128);
    doc.text("SahlaMove – Service professionnel de déménagement", 15, pageHeight - 18);
    doc.text("Tel: 0558 949 062 | Web: www.sahlamove.com | Email: <EMAIL>", 15, pageHeight - 12);
    doc.text(`Document généré automatiquement le ${today} – © SahlaMove`, 15, pageHeight - 6);
  },

  // Check if new page is needed
  checkNewPage: (doc, y, threshold = 270) => {
    if (y > threshold) {
      // Add footer to current page before creating new page
      PDFUtils.addPageFooter(doc);
      doc.addPage();
      return 20;
    }
    return y;
  }
};

// PDF generation sections
const PDFSections = {
  // Add header section
  addHeader: (doc, missionDate) => {
    const today = new Date().toLocaleDateString('fr-FR');
    
    // PDF Header with company branding
    doc.setFillColor(44, 62, 80);
    doc.rect(0, 0, PDF_CONFIG.pageWidth, PDF_CONFIG.headerHeight, 'F');
    
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(24);
    doc.setFont(undefined, 'bold');
    doc.text("SahlaMove", 15, 20);

    doc.setFontSize(12);
    doc.setFont(undefined, 'normal');
    doc.text("Feuille de mission de déménagement", 15, 28);

    // Contact information in header
    doc.setFontSize(8);
    doc.text("www.sahlamove.com", 15, 33);

    // Date and mission number
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(10);
    doc.text(`Date: ${today}`, 150, 20);
    doc.text(`Mission: ${missionDate}`, 150, 28);
    
    return 50; // Return starting Y position
  },

  // Add client information section
  addClientInfo: (doc, y) => {
    doc.setFontSize(14);
    doc.setFont(undefined, 'bold');
    doc.setTextColor(52, 73, 94);
    doc.text("INFORMATIONS CLIENT", 15, y);
    y += 10;

    doc.setFontSize(11);
    doc.setFont(undefined, 'normal');
    doc.setTextColor(0, 0, 0);
    
    const clientName = document.getElementById('clientName').value || 'Non spécifié';
    const clientPhone = document.getElementById('clientPhone').value || 'Non spécifié';
    const clientEmail = document.getElementById('clientEmail').value || 'Non spécifié';
    
    doc.text(`Nom: ${clientName}`, 15, y);
    doc.text(`Téléphone: ${clientPhone}`, 110, y);
    y += 7;
    doc.text(`Email: ${clientEmail}`, 15, y);
    y += 15;
    
    return y;
  },

  // Add addresses section
  addAddresses: (doc, y) => {
    doc.setFontSize(14);
    doc.setFont(undefined, 'bold');
    doc.setTextColor(52, 73, 94);
    doc.text("ADRESSES", 15, y);
    y += 10;

    doc.setFontSize(11);
    doc.setFont(undefined, 'normal');
    doc.setTextColor(0, 0, 0);

    // Departure Address
    doc.setFont(undefined, 'bold');
    doc.text("Départ:", 15, y);
    doc.setFont(undefined, 'normal');
    y += 7;
    
    const fromStreet = document.getElementById('fromStreet').value || 'Non spécifié';
    const fromApt = document.getElementById('fromApt').value || '';
    const fromFloor = document.getElementById('fromFloor').value || '0';
    const fromElevator = document.getElementById('fromElevator').value;
    
    y = PDFUtils.addWrappedText(doc, `${fromStreet}`, 15, y, 180);
    if (fromApt) {
      doc.text(`Appartement: ${fromApt}`, 15, y);
      y += 7;
    }
    doc.text(`Étage: ${fromFloor} | Ascenseur: ${fromElevator}`, 15, y);
    y += 10;

    // Arrival Address
    doc.setFont(undefined, 'bold');
    doc.text("Arrivée:", 15, y);
    doc.setFont(undefined, 'normal');
    y += 7;
    
    const toStreet = document.getElementById('toStreet').value || 'Non spécifié';
    const toApt = document.getElementById('toApt').value || '';
    const toFloor = document.getElementById('toFloor').value || '0';
    const toElevator = document.getElementById('toElevator').value;
    
    y = PDFUtils.addWrappedText(doc, `${toStreet}`, 15, y, 180);
    if (toApt) {
      doc.text(`Appartement: ${toApt}`, 15, y);
      y += 7;
    }
    doc.text(`Étage: ${toFloor} | Ascenseur: ${toElevator}`, 15, y);
    y += 15;
    
    return y;
  },

  // Add services section
  addServices: (doc, y) => {
    doc.setFontSize(14);
    doc.setFont(undefined, 'bold');
    doc.setTextColor(52, 73, 94);
    doc.text("DISTANCE ET SERVICES", 15, y);
    y += 10;

    doc.setFontSize(11);
    doc.setFont(undefined, 'normal');
    doc.setTextColor(0, 0, 0);

    const distance = document.getElementById('distance').value || 'Non spécifié';
    const estimatedTime = document.getElementById('estimatedTime').value || 'Non spécifié';
    
    doc.text(`Distance estimée: ${distance} km`, 15, y);
    doc.text(`Durée estimée: ${estimatedTime} heures`, 110, y);
    y += 10;

    // Services
    const packing = document.getElementById('packing').checked ? "Oui" : "Non";
    const assembly = document.getElementById('assembly').checked ? "Oui" : "Non";
    const fragile = document.getElementById('fragile').checked ? "Oui" : "Non";

    doc.text(`Emballage/Déballage: ${packing}`, 15, y);
    y += 7;
    doc.text(`Montage/Démontage: ${assembly}`, 15, y);
    y += 7;
    doc.text(`Protection spéciale: ${fragile}`, 15, y);
    y += 15;
    
    return y;
  },

  // Add inventory section
  addInventory: (doc, y) => {
    // Check if we need a new page first
    y = PDFUtils.checkNewPage(doc, y, 200);

    // Add header (either on current page or new page)
    doc.setFontSize(14);
    doc.setFont(undefined, 'bold');
    doc.setTextColor(52, 73, 94);
    doc.text("INVENTAIRE DES BIENS", 15, y);
    y += 10;

    // Inventory table header
    const tableWidths = [8, 75, 18, 22, 52, 25];
    doc.setFontSize(9); // Smaller font for header
    y = PDFUtils.drawTableRow(doc, ['#', 'Description', 'Qté', 'Assemblage', 'Observations'], 15, y, tableWidths, 9, true);

    doc.setTextColor(0, 0, 0);
    doc.setFont(undefined, 'normal');
    doc.setFontSize(8); // Smaller font for content

    // Inventory items
    const items = document.querySelectorAll('.inventoryItem');
    let itemCount = 0;
    
    console.log(`Found ${items.length} inventory items`);
    
    items.forEach((item, index) => {
      const desc = item.querySelector('.itemDesc').value;
      const qty = item.querySelector('.itemQty').value;
      const assemblyVal = item.querySelector('.itemAssembly').value;
      const observations = item.querySelector('.itemObservations').value;
      
      console.log(`Item ${index}: desc="${desc}", qty="${qty}", assembly="${assemblyVal}", observations="${observations}"`);
      
      if (desc && qty) { // Only include items with description and quantity
        itemCount++;
        
        // Check if we need a new page
        y = PDFUtils.checkNewPage(doc, y, 270);
        if (y === 20) {
          // Redraw header on new page
          doc.setFontSize(9); // Smaller font for header
          y = PDFUtils.drawTableRow(doc, ['#', 'Description', 'Qté', 'Assemblage', 'Observations'], 15, y, tableWidths, 9, true);
          doc.setFontSize(8); // Reset to smaller font for content
        }
        
        const rowData = [
          itemCount.toString(),
          desc.length > 35 ? desc.substring(0, 32) + '...' : desc,
          qty,
          assemblyVal,
          observations || '' // Use actual observations or empty string
        ];
        
        console.log(`Adding row: ${rowData}`);
        y = PDFUtils.drawTableRow(doc, rowData, 15, y, tableWidths, 7, false);
      }
    });
    
    console.log(`Total items added to PDF: ${itemCount}`);

    // Add some empty rows for manual additions
    for (let i = 0; i < 3; i++) {
      y = PDFUtils.checkNewPage(doc, y, 270);
      y = PDFUtils.drawTableRow(doc, ['', '', '', '', ''], 15, y, tableWidths, 7, false);
    }

    y += 10;
    return y;
  },

  // Add signatures section
  addSignatures: (doc, y) => {
    y = PDFUtils.checkNewPage(doc, y, 240);

    doc.setFontSize(14);
    doc.setFont(undefined, 'bold');
    doc.setTextColor(52, 73, 94);
    doc.text("SIGNATURES", 15, y);
    y += 15;

    doc.setFontSize(11);
    doc.setFont(undefined, 'normal');
    doc.setTextColor(0, 0, 0);

    // Signature boxes
    const signatureWidth = 55;
    const signatureHeight = 25;
    
    // Client signature
    doc.rect(15, y, signatureWidth, signatureHeight);
    doc.text("Client", 15, y - 3);
    doc.text("Nom:", 17, y + 8);
    doc.text("Date:", 17, y + 15);
    doc.text("Signature:", 17, y + 22);

    
    // Office Manager signature
    doc.rect(80, y, signatureWidth, signatureHeight);
    doc.text("Office Manager", 80, y - 3);
    doc.text("Nom:", 82, y + 8);
    doc.text("Date:", 82, y + 15);
    doc.text("Signature:", 82, y + 22);

    // Worker signature
    doc.rect(145, y, signatureWidth, signatureHeight);
    doc.text("Ouvrier", 145, y - 3);
    doc.text("Nom:", 147, y + 8);
    doc.text("Date:", 147, y + 15);
    doc.text("Signature:", 147, y + 22);

    // Company stamp zone
    y += signatureHeight + 15;
    const stampWidth = 60;
    const stampHeight = 30;

    doc.setDrawColor(100, 100, 100);
    doc.setLineWidth(1);
    doc.rect(15, y, stampWidth, stampHeight);

    doc.setFontSize(10);
    doc.setFont(undefined, 'bold');
    doc.text("CACHET DE L'ENTREPRISE", 17, y - 3);

    doc.setFontSize(8);
    doc.setFont(undefined, 'normal');
    doc.setTextColor(150, 150, 150);
    doc.text("Zone réservée au cachet", 17, y + 8);
    doc.text("officiel de SahlaMove", 17, y + 15);
    doc.text("pour validation", 17, y + 22);

    // Footer - will be handled by addPageFooter function
    y += stampHeight + 10;
    
    return y;
  }
};

async function generatePDF() {
  const generateBtn = document.querySelector('.generate-pdf-btn');
  const originalText = generateBtn.innerHTML;
  
  
  
  try {
    console.log("Starting PDF generation...");
    
    // Show loading state
    generateBtn.innerHTML = '<span>⏳</span> Génération en cours...';
    generateBtn.disabled = true;
    
    // Validate form before generating PDF
    const missingFields = FormUtils.validateForm();
    if (missingFields.length > 0) {
      FormUtils.showValidationMessage(missingFields);
      FormUtils.scrollToFirstError(missingFields);
      return;
    }
    
    const { jsPDF } = window.jspdf;

    if (!jsPDF) {
      console.error("jsPDF not loaded!");
      alert("Erreur: jsPDF n'est pas chargé. Vérifiez votre connexion internet.");
      return;
    }

    const doc = new jsPDF();
    console.log("jsPDF initialized successfully");

    // Get mission date
    const missionDateInput = document.getElementById('missionDate').value;
    const missionDate = missionDateInput || new Date().toISOString().split('T')[0];

    // Generate structured filename
    const fileInfo = FileUtils.generateMissionFilename(missionDate);

    // Generate PDF sections in order
    let y = PDFSections.addHeader(doc, missionDate);
    y = PDFSections.addClientInfo(doc, y);
    y = PDFSections.addAddresses(doc, y);
    y = PDFSections.addServices(doc, y);
    y = PDFSections.addInventory(doc, y);
    y = PDFSections.addSignatures(doc, y);

    // Add footer to the final page
    PDFUtils.addPageFooter(doc);

    console.log(`Saving PDF as: ${fileInfo.filename}`);
    console.log(`Structured path: ${fileInfo.fullPath}`);

    // Save with structured filename
    doc.save(fileInfo.filename);
    console.log("PDF generation completed successfully!");

    // Show success message with file info
    alert(`PDF généré avec succès!\nFichier: ${fileInfo.filename}\nStructure: ${fileInfo.fullPath}`);

  } catch (error) {
    console.error("Error generating PDF:", error);
    alert("Erreur lors de la génération du PDF: " + error.message);
  } finally {
    // Restore button state
    generateBtn.innerHTML = originalText;
    generateBtn.disabled = false;
  }
}

// Remove inventory item function
function removeInventoryItem(button) {
  const inventoryItem = button.closest('.inventoryItem');
  const container = document.getElementById('inventoryContainer');
  
  // Don't remove if it's the last item
  if (container.children.length > 1) {
    inventoryItem.remove();
  } else {
    // Clear the last item instead of removing it
    inventoryItem.querySelector('.itemDesc').value = '';
    inventoryItem.querySelector('.itemQty').value = '';
    inventoryItem.querySelector('.itemAssembly').value = 'Non';
    inventoryItem.querySelector('.itemObservations').value = '';
  }
}

// Enhanced form validation and user experience
const FormUtils = {
  // Validate required fields
  validateForm: () => {
    const requiredFields = [
      'clientName', 'clientPhone', 'missionDate', 
      'fromStreet', 'toStreet'
    ];
    
    const missingFields = [];
    requiredFields.forEach(fieldId => {
      const field = document.getElementById(fieldId);
      if (!field.value.trim()) {
        missingFields.push(fieldId);
        field.style.borderColor = '#e74c3c';
      } else {
        field.style.borderColor = '#e1e8ed';
      }
    });
    
    return missingFields;
  },

  // Show validation message
  showValidationMessage: (missingFields) => {
    const fieldNames = {
      'clientName': 'Nom du client',
      'clientPhone': 'Téléphone',
      'missionDate': 'Date de mission',
      'fromStreet': 'Adresse de départ',
      'toStreet': 'Adresse d\'arrivée'
    };
    
    const missingNames = missingFields.map(id => fieldNames[id]).join(', ');
    alert(`Veuillez remplir les champs obligatoires suivants: ${missingNames}`);
  },

  // Add smooth scroll to first error
  scrollToFirstError: (missingFields) => {
    if (missingFields.length > 0) {
      const firstError = document.getElementById(missingFields[0]);
      firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
      firstError.focus();
    }
  }
};

// Add new inventory item
document.addEventListener('DOMContentLoaded', function() {
  document.getElementById('addItem').addEventListener('click', () => {
    const container = document.getElementById('inventoryContainer');
    const div = document.createElement('div');
    div.className = 'inventoryItem';
    div.innerHTML = `
      <input type="text" placeholder="Ex: Canapé 3 places" class="itemDesc" required>
      <input type="number" placeholder="1" class="itemQty" min="1" required>
      <select class="itemAssembly">
        <option value="Non">Non</option>
        <option value="Oui">Oui</option>
      </select>
      <input type="text" placeholder="Notes, état, etc." class="itemObservations">
      <button type="button" class="remove-item" onclick="removeInventoryItem(this)" title="Supprimer cet objet">🗑️</button>
    `;
    container.appendChild(div);
    
    // Focus on the new description field
    const newDescField = div.querySelector('.itemDesc');
    newDescField.focus();
  });

  // Set today's date as default
  const today = new Date().toISOString().split('T')[0];
  document.getElementById('missionDate').value = today;
  
  // Add form validation on input
  const requiredFields = ['clientName', 'clientPhone', 'fromStreet', 'toStreet'];
  requiredFields.forEach(fieldId => {
    const field = document.getElementById(fieldId);
    field.addEventListener('input', function() {
      if (this.value.trim()) {
        this.style.borderColor = '#27ae60';
        setTimeout(() => {
          this.style.borderColor = '#e1e8ed';
        }, 1000);
      }
    });
  });
});
