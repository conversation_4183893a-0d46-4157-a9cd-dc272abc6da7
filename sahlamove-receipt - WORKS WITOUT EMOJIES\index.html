<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SahlaMove - Générateur de reçu</title>
  <link rel="stylesheet" href="style.css">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🚚 SahlaMove</h1>
      <p class="subtitle">Formulaire de mission de déménagement</p>
    </div>
    
    <form id="missionForm">
      <!-- Client Information Section -->
      <div class="form-section">
        <h2><span class="section-icon">👤</span> Informations client</h2>
        <div class="form-grid">
          <div class="form-group">
            <label for="clientName">Nom du client <span class="required">*</span></label>
            <input type="text" id="clientName" required placeholder="Nom complet du client">
          </div>
          <div class="form-group">
            <label for="clientPhone">Téléphone <span class="required">*</span></label>
            <input type="tel" id="clientPhone" required placeholder="05 58 94 90 62">
          </div>
          <div class="form-group">
            <label for="clientEmail">Email</label>
            <input type="email" id="clientEmail" placeholder="<EMAIL>">
          </div>
          <div class="form-group">
            <label for="missionDate">Date de mission <span class="required">*</span></label>
            <input type="date" id="missionDate" required>
          </div>
        </div>
      </div>

      <!-- Departure Address Section -->
      <div class="form-section">
        <h2><span class="section-icon">📍</span> Adresse de départ</h2>
        <div class="form-grid">
          <div class="form-group">
            <label for="fromWilaya">Wilaya <span class="required">*</span></label>
            <select id="fromWilaya" required>
              <option value="">Sélectionner une wilaya</option>
              <option value="Adrar">01 - Adrar</option>
              <option value="Chlef">02 - Chlef</option>
              <option value="Laghouat">03 - Laghouat</option>
              <option value="Oum El Bouaghi">04 - Oum El Bouaghi</option>
              <option value="Batna">05 - Batna</option>
              <option value="Béjaïa">06 - Béjaïa</option>
              <option value="Biskra">07 - Biskra</option>
              <option value="Béchar">08 - Béchar</option>
              <option value="Blida">09 - Blida</option>
              <option value="Bouira">10 - Bouira</option>
              <option value="Tamanrasset">11 - Tamanrasset</option>
              <option value="Tébessa">12 - Tébessa</option>
              <option value="Tlemcen">13 - Tlemcen</option>
              <option value="Tiaret">14 - Tiaret</option>
              <option value="Tizi Ouzou">15 - Tizi Ouzou</option>
              <option value="Alger">16 - Alger</option>
              <option value="Djelfa">17 - Djelfa</option>
              <option value="Jijel">18 - Jijel</option>
              <option value="Sétif">19 - Sétif</option>
              <option value="Saïda">20 - Saïda</option>
              <option value="Skikda">21 - Skikda</option>
              <option value="Sidi Bel Abbès">22 - Sidi Bel Abbès</option>
              <option value="Annaba">23 - Annaba</option>
              <option value="Guelma">24 - Guelma</option>
              <option value="Constantine">25 - Constantine</option>
              <option value="Médéa">26 - Médéa</option>
              <option value="Mostaganem">27 - Mostaganem</option>
              <option value="M'Sila">28 - M'Sila</option>
              <option value="Mascara">29 - Mascara</option>
              <option value="Ouargla">30 - Ouargla</option>
              <option value="Oran">31 - Oran</option>
              <option value="El Bayadh">32 - El Bayadh</option>
              <option value="Illizi">33 - Illizi</option>
              <option value="Bordj Bou Arréridj">34 - Bordj Bou Arréridj</option>
              <option value="Boumerdès">35 - Boumerdès</option>
              <option value="El Tarf">36 - El Tarf</option>
              <option value="Tindouf">37 - Tindouf</option>
              <option value="Tissemsilt">38 - Tissemsilt</option>
              <option value="El Oued">39 - El Oued</option>
              <option value="Khenchela">40 - Khenchela</option>
              <option value="Souk Ahras">41 - Souk Ahras</option>
              <option value="Tipaza">42 - Tipaza</option>
              <option value="Mila">43 - Mila</option>
              <option value="Aïn Defla">44 - Aïn Defla</option>
              <option value="Naâma">45 - Naâma</option>
              <option value="Aïn Témouchent">46 - Aïn Témouchent</option>
              <option value="Ghardaïa">47 - Ghardaïa</option>
              <option value="Relizane">48 - Relizane</option>
              <option value="Timimoun">49 - Timimoun</option>
              <option value="Bordj Badji Mokhtar">50 - Bordj Badji Mokhtar</option>
              <option value="Ouled Djellal">51 - Ouled Djellal</option>
              <option value="Béni Abbès">52 - Béni Abbès</option>
              <option value="In Salah">53 - In Salah</option>
              <option value="In Guezzam">54 - In Guezzam</option>
              <option value="Touggourt">55 - Touggourt</option>
              <option value="Djanet">56 - Djanet</option>
              <option value="El M'Ghair">57 - El M'Ghair</option>
              <option value="El Meniaa">58 - El Meniaa</option>
            </select>
          </div>
          <div class="form-group">
            <label for="fromCommune">Commune <span class="required">*</span></label>
            <input type="text" id="fromCommune" required placeholder="Ex: Es-Sénia, Hydra, Bir Mourad Raïs">
          </div>
          <div class="form-group full-width">
            <label for="fromStreet">Adresse <span class="required">*</span></label>
            <input type="text" id="fromStreet" required placeholder="Numéro, rue, quartier">
          </div>
          <div class="form-group">
            <label for="fromApt">Appartement/Bâtiment</label>
            <input type="text" id="fromApt" placeholder="Apt 12, Bât A">
          </div>
          <div class="form-group">
            <label for="fromFloor">Étage</label>
            <input type="number" id="fromFloor" min="0" max="50" placeholder="0">
          </div>
          <div class="form-group">
            <label for="fromElevator">Ascenseur disponible</label>
            <select id="fromElevator">
              <option value="Oui">Oui</option>
              <option value="Non">Non</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Arrival Address Section -->
      <div class="form-section">
        <h2><span class="section-icon">🎯</span> Adresse d'arrivée</h2>
        <div class="form-grid">
          <div class="form-group">
            <label for="toWilaya">Wilaya <span class="required">*</span></label>
            <select id="toWilaya" required>
              <option value="">Sélectionner une wilaya</option>
              <option value="Adrar">01 - Adrar</option>
              <option value="Chlef">02 - Chlef</option>
              <option value="Laghouat">03 - Laghouat</option>
              <option value="Oum El Bouaghi">04 - Oum El Bouaghi</option>
              <option value="Batna">05 - Batna</option>
              <option value="Béjaïa">06 - Béjaïa</option>
              <option value="Biskra">07 - Biskra</option>
              <option value="Béchar">08 - Béchar</option>
              <option value="Blida">09 - Blida</option>
              <option value="Bouira">10 - Bouira</option>
              <option value="Tamanrasset">11 - Tamanrasset</option>
              <option value="Tébessa">12 - Tébessa</option>
              <option value="Tlemcen">13 - Tlemcen</option>
              <option value="Tiaret">14 - Tiaret</option>
              <option value="Tizi Ouzou">15 - Tizi Ouzou</option>
              <option value="Alger">16 - Alger</option>
              <option value="Djelfa">17 - Djelfa</option>
              <option value="Jijel">18 - Jijel</option>
              <option value="Sétif">19 - Sétif</option>
              <option value="Saïda">20 - Saïda</option>
              <option value="Skikda">21 - Skikda</option>
              <option value="Sidi Bel Abbès">22 - Sidi Bel Abbès</option>
              <option value="Annaba">23 - Annaba</option>
              <option value="Guelma">24 - Guelma</option>
              <option value="Constantine">25 - Constantine</option>
              <option value="Médéa">26 - Médéa</option>
              <option value="Mostaganem">27 - Mostaganem</option>
              <option value="M'Sila">28 - M'Sila</option>
              <option value="Mascara">29 - Mascara</option>
              <option value="Ouargla">30 - Ouargla</option>
              <option value="Oran">31 - Oran</option>
              <option value="El Bayadh">32 - El Bayadh</option>
              <option value="Illizi">33 - Illizi</option>
              <option value="Bordj Bou Arréridj">34 - Bordj Bou Arréridj</option>
              <option value="Boumerdès">35 - Boumerdès</option>
              <option value="El Tarf">36 - El Tarf</option>
              <option value="Tindouf">37 - Tindouf</option>
              <option value="Tissemsilt">38 - Tissemsilt</option>
              <option value="El Oued">39 - El Oued</option>
              <option value="Khenchela">40 - Khenchela</option>
              <option value="Souk Ahras">41 - Souk Ahras</option>
              <option value="Tipaza">42 - Tipaza</option>
              <option value="Mila">43 - Mila</option>
              <option value="Aïn Defla">44 - Aïn Defla</option>
              <option value="Naâma">45 - Naâma</option>
              <option value="Aïn Témouchent">46 - Aïn Témouchent</option>
              <option value="Ghardaïa">47 - Ghardaïa</option>
              <option value="Relizane">48 - Relizane</option>
              <option value="Timimoun">49 - Timimoun</option>
              <option value="Bordj Badji Mokhtar">50 - Bordj Badji Mokhtar</option>
              <option value="Ouled Djellal">51 - Ouled Djellal</option>
              <option value="Béni Abbès">52 - Béni Abbès</option>
              <option value="In Salah">53 - In Salah</option>
              <option value="In Guezzam">54 - In Guezzam</option>
              <option value="Touggourt">55 - Touggourt</option>
              <option value="Djanet">56 - Djanet</option>
              <option value="El M'Ghair">57 - El M'Ghair</option>
              <option value="El Meniaa">58 - El Meniaa</option>
            </select>
          </div>
          <div class="form-group">
            <label for="toCommune">Commune <span class="required">*</span></label>
            <input type="text" id="toCommune" required placeholder="Ex: Es-Sénia, Hydra, Bir Mourad Raïs">
          </div>
          <div class="form-group full-width">
            <label for="toStreet">Adresse <span class="required">*</span></label>
            <input type="text" id="toStreet" required placeholder="Numéro, rue, quartier">
          </div>
          <div class="form-group">
            <label for="toApt">Appartement/Bâtiment</label>
            <input type="text" id="toApt" placeholder="Apt 12, Bât A">
          </div>
          <div class="form-group">
            <label for="toFloor">Étage</label>
            <input type="number" id="toFloor" min="0" max="50" placeholder="0">
          </div>
          <div class="form-group">
            <label for="toElevator">Ascenseur disponible</label>
            <select id="toElevator">
              <option value="Oui">Oui</option>
              <option value="Non">Non</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Distance and Services Section -->
      <div class="form-section">
        <h2><span class="section-icon">📏</span> Distance et services</h2>
        <div class="form-grid">
          <div class="form-group">
            <label for="distance">Distance estimée (km)</label>
            <input type="number" id="distance" min="0" step="0.1" placeholder="15.5">
          </div>
          <div class="form-group">
            <label for="estimatedTime">Durée estimée (heures)</label>
            <input type="number" id="estimatedTime" min="0" step="0.5" placeholder="4">
          </div>
        </div>
        
        <div class="services-grid">
          <div class="service-item">
            <label class="checkbox-label">
              <input type="checkbox" id="packing">
              <span class="checkmark"></span>
              <span class="service-text">
                <strong>Emballage / Déballage</strong>
                <small>Protection et emballage des objets fragiles</small>
              </span>
            </label>
          </div>
          <div class="service-item">
            <label class="checkbox-label">
              <input type="checkbox" id="assembly">
              <span class="checkmark"></span>
              <span class="service-text">
                <strong>Montage / Démontage</strong>
                <small>Démontage et remontage de meubles</small>
              </span>
            </label>
          </div>
          <div class="service-item">
            <label class="checkbox-label">
              <input type="checkbox" id="fragile">
              <span class="checkmark"></span>
              <span class="service-text">
                <strong>Protection spéciale</strong>
                <small>Objets de valeur ou très fragiles</small>
              </span>
            </label>
          </div>
        </div>
      </div>

      <!-- Inventory Section -->
      <div class="form-section">
        <h2><span class="section-icon">📦</span> Inventaire des biens</h2>
        <div class="inventory-header">
          <div class="inventory-labels">
            <span>Description de l'objet</span>
            <span>Quantité</span>
            <span>Assemblage requis</span>
            <span>Observations</span>
            <span>Action</span>
          </div>
        </div>
        <div id="inventoryContainer">
          <div class="inventoryItem">
            <input type="text" placeholder="Ex: Canapé 3 places" class="itemDesc" required>
            <input type="number" placeholder="1" class="itemQty" min="1" required>
            <select class="itemAssembly">
              <option value="Non">Non</option>
              <option value="Oui">Oui</option>
            </select>
            <input type="text" placeholder="Notes, état, etc." class="itemObservations">
            <button type="button" class="remove-item" onclick="removeInventoryItem(this)" title="Supprimer cet objet">🗑️</button>
          </div>
        </div>
        <button type="button" id="addItem" class="add-item-btn">
          <span>➕</span> Ajouter un objet
        </button>
      </div>

      <!-- Generate PDF Button -->
      <div class="form-actions">
      <button type="button" class="generate-pdf-btn" onclick="generatePDF()">
        <span>📄</span> Générer le reçu PDF
      </button>
      <div class="form-info">
        <p><strong>Note:</strong> Tous les champs marqués d'un astérisque (*) sont obligatoires.</p>
        <p>Les objets ajoutés à l'inventaire apparaîtront automatiquement dans le PDF généré.</p>
      </div>
      </div>
    </form>
  </div>

  <script src="script.js"></script>
</body>
</html>
