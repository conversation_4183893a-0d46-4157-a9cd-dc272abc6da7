/* Modern SahlaMove Receipt Generator Styles */

/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
    line-height: 1.6;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 40px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    position: relative;
    z-index: 1;
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
    position: relative;
    z-index: 1;
}

/* Form Section Styles */
.form-section {
    padding: 30px 40px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.form-section:hover {
    background-color: #fafafa;
}

.form-section:last-child {
    border-bottom: none;
}

.form-section h2 {
    color: #2c3e50;
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-icon {
    font-size: 1.2rem;
}

/* Form Grid Layout */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 500;
    color: #34495e;
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.required {
    color: #e74c3c;
    font-weight: 600;
}

/* Input Styles */
input[type="text"],
input[type="tel"],
input[type="email"],
input[type="date"],
input[type="number"],
select {
    padding: 12px 16px;
    border: 2px solid #e1e8ed;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: #ffffff;
    font-family: inherit;
}

input[type="text"]:focus,
input[type="tel"]:focus,
input[type="email"]:focus,
input[type="date"]:focus,
input[type="number"]:focus,
select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    transform: translateY(-1px);
}

input::placeholder {
    color: #95a5a6;
    font-style: italic;
}

/* Services Grid */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.service-item {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.service-item:hover {
    background: #e3f2fd;
    border-color: #2196f3;
    transform: translateY(-2px);
}

/* Custom Checkbox Styles */
.checkbox-label {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    gap: 15px;
    position: relative;
}

.checkbox-label input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.checkmark {
    width: 24px;
    height: 24px;
    background-color: #ffffff;
    border: 2px solid #bdc3c7;
    border-radius: 6px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked ~ .checkmark {
    background-color: #27ae60;
    border-color: #27ae60;
}

.checkmark::after {
    content: '';
    position: absolute;
    display: none;
    left: 7px;
    top: 3px;
    width: 6px;
    height: 12px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-label input[type="checkbox"]:checked ~ .checkmark::after {
    display: block;
}

.service-text {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.service-text strong {
    color: #2c3e50;
    font-weight: 600;
}

.service-text small {
    color: #7f8c8d;
    font-size: 0.85rem;
}

/* Inventory Styles */
.inventory-header {
    margin-bottom: 15px;
}

.inventory-labels {
    display: grid;
    grid-template-columns: 2fr 1fr 1.5fr 2fr 80px;
    gap: 15px;
    padding: 12px 15px;
    background: #34495e;
    color: white;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.9rem;
}

.inventoryItem {
    display: grid;
    grid-template-columns: 2fr 1fr 1.5fr 2fr 80px;
    gap: 15px;
    align-items: center;
    padding: 15px;
    background: #ffffff;
    border: 2px solid #ecf0f1;
    border-radius: 10px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.inventoryItem:hover {
    border-color: #3498db;
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.1);
}

.inventoryItem input,
.inventoryItem select {
    margin: 0;
    padding: 10px 12px;
}

.inventoryItem .itemObservations {
    font-style: italic;
    color: #7f8c8d;
}

.inventoryItem .itemObservations::placeholder {
    color: #bdc3c7;
    font-style: italic;
}

.remove-item {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-item:hover {
    background: #c0392b;
    transform: scale(1.05);
}

/* Button Styles */
.add-item-btn {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 15px 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
}

.add-item-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(46, 204, 113, 0.4);
}

.add-item-btn:active {
    transform: translateY(0);
}

.form-actions {
    padding: 40px;
    text-align: center;
    background: #f8f9fa;
}

.form-info {
    margin-top: 20px;
    padding: 15px;
    background: #e8f4fd;
    border-radius: 10px;
    border-left: 4px solid #3498db;
}

.form-info p {
    margin: 5px 0;
    color: #2c3e50;
    font-size: 0.9rem;
    line-height: 1.4;
}

.generate-pdf-btn {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    border: none;
    border-radius: 15px;
    padding: 18px 40px;
    font-size: 1.2rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.generate-pdf-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
}

.generate-pdf-btn:active {
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    .container {
        border-radius: 15px;
    }

    .header {
        padding: 30px 20px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .form-section {
        padding: 20px;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .inventory-labels,
    .inventoryItem {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .inventory-labels {
        display: none; /* Hide labels on mobile, use placeholders instead */
    }

    .inventoryItem {
        padding: 15px;
    }

    .inventoryItem input::placeholder {
        font-weight: 600;
        color: #7f8c8d;
    }

    .remove-item {
        justify-self: end;
        width: 100%;
        margin-top: 10px;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.8rem;
    }

    .subtitle {
        font-size: 1rem;
    }

    .form-section h2 {
        font-size: 1.2rem;
    }

    .generate-pdf-btn {
        padding: 15px 30px;
        font-size: 1.1rem;
    }
}

/* Print Styles */
@media print {
    body {
        background: white;
        padding: 0;
    }

    .container {
        box-shadow: none;
        border-radius: 0;
    }

    .generate-pdf-btn {
        display: none;
    }
}
